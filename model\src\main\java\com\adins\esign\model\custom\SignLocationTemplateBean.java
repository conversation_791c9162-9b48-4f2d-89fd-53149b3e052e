package com.adins.esign.model.custom;

import java.io.Serializable;

import com.adins.esign.model.MsLov;

/**
 * Template-based sign location bean for consolidated signers
 */
public class SignLocationTemplateBean implements Serializable {
	private static final long serialVersionUID = 1L;
	
	private String signLocation;
	private String tekenAjaSignLocation;
	private String vidaSignLocation;
	private String privySignLocation;
	private Integer signPage;
	private Short seqNo;
	private String transform;
	private MsLov msLovByLovSignType;
	private MsLov msLovByLovSignerType;
	
	public String getSignLocation() {
		return signLocation;
	}
	
	public void setSignLocation(String signLocation) {
		this.signLocation = signLocation;
	}
	
	public String getTekenAjaSignLocation() {
		return tekenAjaSignLocation;
	}
	
	public void setTekenAjaSignLocation(String tekenAjaSignLocation) {
		this.tekenAjaSignLocation = tekenAjaSignLocation;
	}
	
	public String getVidaSignLocation() {
		return vidaSignLocation;
	}
	
	public void setVidaSignLocation(String vidaSignLocation) {
		this.vidaSignLocation = vidaSignLocation;
	}
	
	public String getPrivySignLocation() {
		return privySignLocation;
	}
	
	public void setPrivySignLocation(String privySignLocation) {
		this.privySignLocation = privySignLocation;
	}
	
	public Integer getSignPage() {
		return signPage;
	}
	
	public void setSignPage(Integer signPage) {
		this.signPage = signPage;
	}
	
	public Short getSeqNo() {
		return seqNo;
	}
	
	public void setSeqNo(Short seqNo) {
		this.seqNo = seqNo;
	}
	
	public String getTransform() {
		return transform;
	}
	
	public void setTransform(String transform) {
		this.transform = transform;
	}
	
	public MsLov getMsLovByLovSignType() {
		return msLovByLovSignType;
	}
	
	public void setMsLovByLovSignType(MsLov msLovByLovSignType) {
		this.msLovByLovSignType = msLovByLovSignType;
	}
	
	public MsLov getMsLovByLovSignerType() {
		return msLovByLovSignerType;
	}
	
	public void setMsLovByLovSignerType(MsLov msLovByLovSignerType) {
		this.msLovByLovSignerType = msLovByLovSignerType;
	}
}
